version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql_easyswap
    environment:
      MYSQL_ROOT_PASSWORD: rootpasswd
      MYSQL_DATABASE: easyswap
      MYSQL_USER: easyuser
      MYSQL_PASSWORD: easypasswd
    ports:
      - "3307:3306"  # 使用3307端口避免冲突
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2
    container_name: redis_easyswap
    ports:
      - "6380:6379"  # 使用6380端口避免冲突
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data: