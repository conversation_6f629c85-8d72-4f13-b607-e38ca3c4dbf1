[project_cfg]
name = "OrderBookDex"

[monitor]
pprof_enable = true
pprof_port = 6060

[log]
compress = false
leep_days = 7
level = "info"
mode = "console"
path = "logs/relayer"
service_name = "easyswap-sync"

[db]
database = "easyswap"
password = "easypasswd"
port = 3306
max_open_conns = 1500
host = "127.0.0.1"
log_level = "info"
max_conn_max_lifetime = 300
user = "easyuser"
max_idle_conns = 10

[kv]
[[kv.redis]]
pass = ""
host = "127.0.0.1:6379"
type = "node"

[ankr_cfg]
api_key=""  # 如果需要，在这里填入你的 Ankr API Key
https_url="https://rpc.ankr.com/eth_sepolia"  # Sepolia 测试网
#https_url="https://rpc.ankr.com/optimism"

[chain_cfg]
name="sepolia"
id=11155111

[contract_cfg]
eth_address = "******************************************"
weth_address = "******************************************"
dex_address = "******************************************" # 需要部署实际的合约地址
