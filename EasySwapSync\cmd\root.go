package cmd

import (
	"fmt"
	"os"
	"strings"

	"github.com/mitchellh/go-homedir"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

// 假设用户输入.\build\sync_service.exe daemon -c ".\config\config_import.toml"
// cfgFile 最终保存的是 .\config\config_import.toml
var cfgFile string // 定义一个全局配置文件路径、不管是开发、还是测试、正式环境都是这个路径

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "sync",
	Short: "root server.",
	Long:  `root server.`,
	// Uncomment the following line if your bare application
	// has an action associated with it:
	// Run: func(cmd *cobra.Command, args []string) { },
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
/*
	2. main.go调用cmd.Execute()，开始解析cobra定义的参数，赋值给指定参数cfgFile
*/
func Execute() {
	if err := rootCmd.Execute(); err != nil { //cobra 解析命令行参数 将值赋给 cfgFile 变量
		fmt.Println(err)
		os.Exit(1) //解析失败 ，退出
	}

	fmt.Println("cfgFile=", cfgFile)
}

// init包导入即执行 不是在命令执行时执行
func init() {
	// 注册initConfig函数，当cobra解析完所有参数后，会调用这个函数
	// Cobra，在执行任何命令之前，请先调用 initConfig 函数"
	cobra.OnInitialize(initConfig)

	/*
		1.init先执行以下两行代码
	*/
	// 获取参数定义工具，可以理解为参数定义
	flags := rootCmd.PersistentFlags()

	// 用户执行了什么环境的命令，&cfgFile就动态变化，如果未指定-c参数，就使用后面默认的配置环境，
	// 这里并不是赋值给cfgFile，而是可以理解为赋值到了cobra内存中，并指定解析到&cfgFile内存地址。
	// 告诉cobra:
	// - 我有一个参数叫 "config"，简写是 "c"
	// - 用户输入的值请存储到 cfgFile 变量中
	// - 如果用户不提供，默认值是 "./config/config_import.toml"
	flags.StringVarP(&cfgFile, "config", "c", "./config/config_import.toml", "config file (default is $HOME/.config_import.toml)")
}

// initConfig reads in config file and ENV variables if set.
/*
 3.开始执行initConfig
*/
func initConfig() {
	if cfgFile != "" {
		//将配置文件路径传递给viper
		viper.SetConfigFile(cfgFile)
	} else {
		// 没有指定配置文件，就使用默认的配置文件，比如：/Users/<USER>/.config_import.toml
		// 找到主目录，比如：/Users/<USER>
		home, err := homedir.Dir()
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}

		// 从主目录下搜索后缀名为 ".toml" 文件 (without extension).
		viper.AddConfigPath(home)
		viper.SetConfigName("config_import")
	}
	viper.AutomaticEnv()                      // 读取匹配的环境变量
	viper.SetConfigType("toml")               //读取配置文件
	viper.SetEnvPrefix("EasySwap")            //设置环境变量的前缀
	replacer := strings.NewReplacer(".", "_") //替换点为下划线
	viper.SetEnvKeyReplacer(replacer)         //设置环境变量替换规则
	// 读取配置文件
	if err := viper.ReadInConfig(); err == nil {
		fmt.Println("Using config file:", viper.ConfigFileUsed())
	} else {
		panic(err)
	}

}
