package cmd

import (
	"context"
	"fmt"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"sync"
	"syscall"

	"github.com/ProjectsTask/EasySwapBase/logger/xzap"
	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"github.com/ProjectsTask/EasySwapSync/service"
	"github.com/ProjectsTask/EasySwapSync/service/config"
)

// DaemonCmd 子命令，用于启动同步服务
var DaemonCmd = &cobra.Command{
	Use:   "daemon",
	Short: "sync easy swap order info.",
	Long:  "sync easy swap order info.",
	Run: func(cmd *cobra.Command, args []string) {
		wg := &sync.WaitGroup{}
		wg.Add(1)
		ctx := context.Background()            // 创建一个context上下文
		ctx, cancel := context.WithCancel(ctx) //创建一个可取消的context

		// rpc退出信号通知chan
		onSyncExit := make(chan error, 1) // 创建一个channel通道 用来接收rpc退出信号 默认大小为1

		go func() {
			defer wg.Done() //协程结束之前 解除阻塞

			cfg, err := config.UnmarshalCmdConfig() // 读取和解析配置文件
			if err != nil {
				xzap.WithContext(ctx).Error("Failed to unmarshal config", zap.Error(err))
				onSyncExit <- err // 将错误发送给onSyncExit通道
				return
			}

			/*
				这一步初始化日志库原因:确保前一步读取和解析配置文件成功，然后默认日志库会记录错误信息。
				紧接着初始化日志库，替换默认日志库，将错误信息记录到文件中。
			*/
			_, err = xzap.SetUp(*cfg.Log) // 初始化日志模块
			if err != nil {
				xzap.WithContext(ctx).Error("Failed to set up logger", zap.Error(err))
				onSyncExit <- err
				return
			}

			xzap.WithContext(ctx).Info("sync server start", zap.Any("config", cfg)) // 输出配置信息

			s, err := service.New(ctx, cfg) // 初始化服务 	
			if err != nil {
				xzap.WithContext(ctx).Error("Failed to create sync server", zap.Error(err))
				onSyncExit <- err
				return
			}

			if err := s.Start(); err != nil { // 启动服务
				xzap.WithContext(ctx).Error("Failed to start sync server", zap.Error(err))
				onSyncExit <- err
				return
			}

			if cfg.Monitor.PprofEnable { // 开启pprof，用于性能监控
				http.ListenAndServe(fmt.Sprintf("0.0.0.0:%d", cfg.Monitor.PprofPort), nil)
			}
		}()

		/*
			两种退出的场景
			1.程序错误 onSyncExit
			2.退出信号 监听主动退出 onSignal
		*/
		// 信号通知chan
		onSignal := make(chan os.Signal)
		// 优雅退出 注册监听信号 如果有信号 则发送信号到通道中
		signal.Notify(onSignal, syscall.SIGINT, syscall.SIGTERM)
		select {
		case sig := <-onSignal: // 监听信号
			switch sig { // 信号处理
			case syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM:
				cancel() //context 取消
				xzap.WithContext(ctx).Info("Exit by signal", zap.String("signal", sig.String()))
			}
		case err := <-onSyncExit: // 监听程序错误通道
			cancel()
			xzap.WithContext(ctx).Error("Exit by error", zap.Error(err))
		}
		wg.Wait() // 等待所有goroutine结束
	},
}

func init() {
	// 将api初始化命令添加到主命令中
	rootCmd.AddCommand(DaemonCmd)
}
