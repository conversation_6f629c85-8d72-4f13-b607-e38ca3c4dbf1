package collectionfilter

import (
	"context"
	"strings"
	"sync"

	"github.com/ProjectsTask/EasySwapBase/stores/gdb"
	"github.com/pkg/errors"

	"gorm.io/gorm"

	"github.com/ProjectsTask/EasySwapSync/service/comm"
)

// Filter 是一个线程安全的结构体，用于存储字符串集合
type Filter struct {
	ctx     context.Context
	db      *gorm.DB
	chain   string
	set     map[string]bool // 字符串集合
	lock    *sync.RWMutex   // 读写互斥锁，保证线程安全
	project string
}

// New 创建一个新的 Filter 并返回其指针
func New(ctx context.Context, db *gorm.DB, chain string, project string) *Filter {
	return &Filter{
		ctx:     ctx,
		db:      db,
		chain:   chain,
		set:     make(map[string]bool),
		lock:    &sync.RWMutex{},
		project: project,
	}
}

// Add 向 Filter 中插入一个新元素
// 元素在插入前会被转换为小写
func (f *Filter) Add(element string) {
	f.lock.Lock()         // 获取写锁
	defer f.lock.Unlock() // 函数返回时释放锁
	f.set[strings.ToLower(element)] = true
}

// Remove 从 Filter 中删除一个元素
func (f *Filter) Remove(element string) {
	f.lock.Lock()
	defer f.lock.Unlock()
	delete(f.set, strings.ToLower(element))
}

// Contains 检查 Filter 是否包含指定元素
// 元素在检查前会被转换为小写
// 查询NFT集合合约地址，是否在本地内存中存在
func (f *Filter) Contains(element string) bool {
	f.lock.RLock()         // 获取读锁 (并发)
	defer f.lock.RUnlock() // 函数返回时释放锁
	_, exists := f.set[strings.ToLower(element)]
	return exists
}

// PreloadCollections 从数据库预加载集合地址到过滤器中
// 从数据库查询我们支持的NFT合约地址，并将其添加到过滤器中
func (f *Filter) PreloadCollections() error {
	var addresses []string
	var err error

	// 直接从数据库查询地址
	err = f.db.WithContext(f.ctx).
		Table(gdb.GetMultiProjectCollectionTableName(f.project, f.chain)).
		Select("address").
		Where("floor_price_status = ?", comm.CollectionFloorPriceImported).
		Scan(&addresses).Error

	if err != nil {
		return errors.Wrap(err, "failed on query collections from db")
	}

	// 将每个地址添加到过滤器中
	for _, address := range addresses {
		f.Add(address)
	}

	return nil
}
