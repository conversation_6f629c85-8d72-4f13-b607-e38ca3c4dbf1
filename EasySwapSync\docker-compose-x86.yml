services:
  mysql:
    image: mysql:8.0
    container_name: mysql_easyswap
    environment:
      MYSQL_ROOT_PASSWORD: rootpasswd
      MYSQL_DATABASE: easyswap
      MYSQL_USER: easyuser
      MYSQL_PASSWORD: easypasswd
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_general_ci
    ports:
      - "3308:3306"  # EasySwapSync专用MySQL端口
    volumes:
      - mysql_data:/var/lib/mysql
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --init-connect='SET NAMES utf8mb4;'
      --innodb-flush-log-at-trx-commit=0
      --sync-binlog=0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "easyuser", "-peasypasswd"]
      timeout: 20s
      retries: 10

  redis:
    image: redis:6.2
    container_name: redis_easyswap
    ports:
      - "6381:6379"  # EasySwapSync专用Redis端口
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

volumes:
  mysql_data:
  redis_data:
