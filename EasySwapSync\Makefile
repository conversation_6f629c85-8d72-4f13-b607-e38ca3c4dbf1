build:
	@echo "build start..."
	@if not exist build mkdir build
	go build main.go
	@move main.exe build\sync_service.exe
	@echo "build done!"

run: build
	@echo "run sync service"
	.\build\sync_service.exe daemon -c ".\config\config_import.toml"

build_linux:
	@echo "build linux amd64 start..."
	@if not exist build mkdir build
	set GOOS=linux&& set GOARCH=amd64&& go build main.go
	@move main build\sync_service_linux
	@echo "build linux amd64 done!"
.PHONY: build